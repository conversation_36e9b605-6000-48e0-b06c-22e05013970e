import { ageLimit } from 'constants/dateOfBirth';
import { MY_MOBILE_CODE } from 'constants/optionList';
import { t } from 'i18next';
import { calculateAgeDiff } from 'utils/helper/calculateAge';
import { date, object, string } from 'yup';
import {
  INSURED_MAX_AGE,
  INSURED_MIN_AGE,
  INVALID_FORMAT,
  MAX_NAME_LENGHT,
  OWNER_MIN_AGE,
  REQUIRED_INPUT,
} from '../common/constant';
import {
  OwnerFormBaseValues,
  ownerFormBaseDefaultValues,
  ownerFormSchemaBase,
} from '../common/ownerSchema.base';

interface MyOwnerFormValues extends Omit<OwnerFormBaseValues, 'dob'> {
  dob?: Date;
  smokingHabit: string;
  nationality: string;
  residencyType: string;
  religion: string;
  occupation: string;
  occupationClass: string;
}

export const myOwnerFormSchema = object().shape({
  ...ownerFormSchemaBase,
  firstName: string()
    .required(REQUIRED_INPUT)
    .max(MAX_NAME_LENGHT, INVALID_FORMAT)
    .validateName(INVALID_FORMAT),
  lastName: string().nullable(),
  extensionName: string().nullable(),
  dob: date()
    .required(REQUIRED_INPUT)
    .test({
      name: 'dob-validation',
      test: (value, ctx) => {
        const ownerIsInsured = ctx.options.context?.ownerIsInsured;
        const agentChannel = ctx.options.context?.agentChannel;

        const minAge = ownerIsInsured
          ? ageLimit[agentChannel]?.SELF?.min?.value
          : ageLimit[agentChannel].DEFAULT?.min?.value;

        const maxAge = ownerIsInsured
          ? ageLimit[agentChannel]?.SELF?.max?.value
          : ageLimit[agentChannel].DEFAULT?.max?.value;

        const minAgeUnit = ownerIsInsured
          ? ageLimit[agentChannel]?.SELF?.min?.unit
          : ageLimit[agentChannel].DEFAULT?.min?.unit;

        const maxAgeUnit = ownerIsInsured
          ? ageLimit[agentChannel]?.SELF?.max?.unit
          : ageLimit[agentChannel].DEFAULT?.max?.unit;

        const minAgeDiff = calculateAgeDiff(value, minAgeUnit);
        const maxAgeDiff = calculateAgeDiff(value, maxAgeUnit);

        if (minAge && minAgeDiff < minAge) {
          return ctx.createError({
            message: t(ownerIsInsured ? INSURED_MIN_AGE : OWNER_MIN_AGE, {
              age: `${minAge} ANB`,
            }),
          });
        }

        if (maxAge && maxAgeDiff > maxAge) {
          return ctx.createError({
            message: t(INSURED_MAX_AGE, {
              age: `${maxAge} ANB`,
            }),
          });
        }

        return !!value;
      },
    }),
  smokingHabit: string().required(REQUIRED_INPUT),
  nationality: string().required(REQUIRED_INPUT),
  religion: string().required(REQUIRED_INPUT),
  occupation: string().required(REQUIRED_INPUT),
  occupationClass: string().required(REQUIRED_INPUT),
  occupationGroupCode: string(),
});

export const myOwnerFormSchemaDefaultValues: MyOwnerFormValues = {
  ...ownerFormBaseDefaultValues,
  code: MY_MOBILE_CODE,
  dob: undefined,
  smokingHabit: '',
  nationality: '',
  religion: '',
  occupation: '',
  occupationClass: '',
  residencyType: '',
};
