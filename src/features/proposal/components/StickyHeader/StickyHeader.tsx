import StickyHeaderTablet, {
  StickyHeaderTabletProps,
} from './StickyHeader.tablet';
import StickyHeaderPhone, { StickyHeaderProps } from './StickyHeader.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function StickyHeader(
  props: StickyHeaderProps & StickyHeaderTabletProps,
) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <StickyHeaderTablet {...props} />
  ) : (
    <StickyHeaderPhone {...props} />
  );
}
