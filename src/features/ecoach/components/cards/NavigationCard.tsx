import React from 'react';
import { Image, TouchableOpacity, View } from 'react-native';
import styled from '@emotion/native';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { Body, H6, SmallLabel } from 'cube-ui-components';
import { Spacer } from 'features/lead/ph/tablet/components/LeadTableTitleRow';
import {
  cheveronRightInCircle,
  mobileQFCard,
  mobileRPCard,
  tabletQFCard,
  tabletRPCard,
} from 'features/ecoach/assets';
import { useTranslation } from 'react-i18next';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

const Container = styled(TouchableOpacity)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    flex: 1,
    marginHorizontal: sizes[2],
    height: isTabletMode ? 144 : 260,
    paddingRight: sizes[4],
    paddingLeft: sizes[4],
    paddingBottom: isTabletMode ? sizes[4] : 0,
    borderRadius: sizes[2],
    borderWidth: 1,
    borderColor: colors.fwdOrange[50],
  }),
);

const CardImage = styled(Image)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: isTabletMode ? 120 : 110,
    height: isTabletMode ? 120 : 110,
  }),
);

const Circle = styled(Image)<{ isTabletMode: boolean }>(({ isTabletMode }) => ({
  alignSelf: 'flex-start',
  marginTop: isTabletMode ? sizes[2] : sizes[15],
  width: sizes[8],
  height: sizes[8],
}));

const Tag = styled(View)(() => ({
  position: 'absolute',
  top: -sizes[3],
  left: sizes[3],
  width: 52,
  height: sizes[6],
  borderRadius: sizes[5],
  backgroundColor: colors.white,
  alignItems: 'center',
  justifyContent: 'center',
}));

export enum NavigationCardType {
  Quickfire = 'quickfire',
  SalesRolePlay = 'salesRolePlay',
}

type NavigationCardProps = {
  navigationCardType: NavigationCardType;
  onPress: () => void;
};

const NavigationCard = ({
  navigationCardType,
  onPress,
}: NavigationCardProps) => {
  const { t } = useTranslation('ecoach');
  const isQF = navigationCardType === NavigationCardType.Quickfire;
  const { isTabletMode } = useLayoutAdoptionCheck();

  const color = isQF ? 'rgba(92, 35, 0, 0.6)' : 'rgba(183, 71, 1, 0.6)';

  let cardImg = tabletRPCard;
  if (!isTabletMode) {
    if (isQF) {
      cardImg = mobileQFCard;
    } else {
      cardImg = mobileRPCard;
    }
  } else {
    if (isQF) {
      cardImg = tabletQFCard;
    } else {
      cardImg = tabletRPCard;
    }
  }

  return (
    <Container
      style={{ backgroundColor: color }}
      isTabletMode={isTabletMode}
      onPress={onPress}>
      <Tag>
        <SmallLabel fontWeight="bold" color={colors.fwdAlternativeOrange[100]}>
          {isQF ? t('fiveMin') : t('eightMin')}
        </SmallLabel>
      </Tag>
      <Spacer height={sizes[5]} />
      <H6
        style={{ width: isTabletMode ? 353 : 133 }}
        fontWeight="bold"
        color={colors.white}>
        {isQF ? t('quickfire') : t('salesRolePlay')}
      </H6>
      <Spacer height={isTabletMode ? sizes[1] : sizes[3]} />
      <Body
        style={{ width: isTabletMode ? '70%' : 139 }}
        fontWeight="normal"
        color={colors.white}>
        {isQF ? t('letSee') : t('enterASimulation')}

        <Body fontWeight="bold" color={colors.fwdYellow[50]}>
          {isQF ? t('fiveMinutes') : `${t('eightMinutes')}`}
        </Body>
      </Body>
      <Circle isTabletMode={isTabletMode} source={cheveronRightInCircle} />
      <CardImage isTabletMode={isTabletMode} source={cardImg} />
    </Container>
  );
};

export default NavigationCard;
