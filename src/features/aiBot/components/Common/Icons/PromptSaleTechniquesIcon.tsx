import Svg, { Path, Rect } from "react-native-svg";

export default function PromptSaleTechniquesIcon({ width, height }: { width?: string; height?: string }) {
	return (
		<Svg width={width || "32"} height={height || "32"} viewBox="0 0 32 32" fill="none">
			<Rect width={width || "32"} height={height || "32"} rx="8" fill="#FED141" />
			<Path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M15.9992 7.19971C11.1352 7.19971 7.19922 11.1437 7.19922 15.9997V23.3437C7.19922 24.1517 7.84722 24.7997 8.65522 24.7997H15.9992C20.8632 24.7997 24.7992 20.8637 24.7992 15.9997C24.7992 11.1437 20.8632 7.19971 15.9992 7.19971ZM15.9992 23.1997H8.79922V15.9997C8.79922 12.0237 12.0232 8.79971 15.9992 8.79971C19.9752 8.79971 23.1992 12.0237 23.1992 15.9997C23.1992 19.9757 19.9752 23.1997 15.9992 23.1997Z"
				fill="white"
			/>
			<Path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M15.9992 7.19971C11.1352 7.19971 7.19922 11.1437 7.19922 15.9997V23.3437C7.19922 24.1517 7.84722 24.7997 8.65522 24.7997H15.9992C20.8632 24.7997 24.7992 20.8637 24.7992 15.9997C24.7992 11.1437 20.8632 7.19971 15.9992 7.19971ZM15.9992 23.1997H8.79922V15.9997C8.79922 12.0237 12.0232 8.79971 15.9992 8.79971C19.9752 8.79971 23.1992 12.0237 23.1992 15.9997C23.1992 19.9757 19.9752 23.1997 15.9992 23.1997Z"
				fill="white"
			/>
			<Path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M17.2915 16.492C17.2915 17.2139 16.7211 17.7843 15.9992 17.7843C15.2773 17.7843 14.7069 17.2139 14.7069 16.492C14.7069 15.779 15.2862 15.1997 15.9992 15.1997C16.7211 15.1997 17.2915 15.7701 17.2915 16.492Z"
				fill="white"
			/>
			<Path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M12.9838 16.492C12.9838 17.2139 12.4115 17.7843 11.6871 17.7843C10.9626 17.7843 10.3992 17.2139 10.3992 16.492C10.3992 15.779 10.9805 15.1997 11.696 15.1997C12.4204 15.1997 12.9838 15.7701 12.9838 16.492Z"
				fill="white"
			/>
			<Path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M21.5992 16.492C21.5992 17.2139 21.0288 17.7843 20.3069 17.7843C19.5939 17.7843 19.0146 17.205 19.0146 16.492C19.0146 15.779 19.5939 15.1997 20.3069 15.1997C21.0377 15.1997 21.5992 15.7701 21.5992 16.492Z"
				fill="white"
			/>
		</Svg>
	);
}
