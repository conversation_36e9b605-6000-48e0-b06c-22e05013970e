import { useTheme } from '@emotion/react';
import Autocomplete from 'components/Autocomplete';
import Input from 'components/Input';
import { getOptionListLabel, getOptionListValue } from 'constants/optionList';
import { Box, Row, TextField } from 'cube-ui-components';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';

import { useGetOptionList } from 'hooks/useGetOptionList';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { PassionSurvey } from 'types/optionList';
import { useMemo } from 'react';
import {
  passionSurveyDefaultValue,
  passionSurveySchema,
} from 'features/eAppV2/id/validations/applicationDetails/passionSurveyValidation';
import PassionSurveyIcon from './PassionSurvey.icon';

export default function PassionSurveyTablet() {
  const { t } = useTranslation('eApp');
  const { space } = useTheme();
  const { data: optionList } = useGetOptionList<'id'>();
  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext();
  const isDone = useSchemaValid(
    control,
    passionSurveyDefaultValue,
    passionSurveySchema,
  );

  const passionOptions = useMemo(
    () => optionList?.PASSION_SURVEY?.options || [],
    [optionList],
  );

  const passionValue = watch('passion');

  const showOtherPassion = passionValue === 'OT';

  const onPassionChange = (value: string | null) => {
    if (value !== 'OT') {
      setValue('otherPassion', '');
    }
  };

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('declaration.passionSurvey.title')}
      icon={<PassionSurveyIcon size={40} />}
      isDone={isDone}>
      <Box px={space[6]} gap={space[5]} pt={space[5]}>
        <Row gap={space[6]}>
          <Input
            style={{ flex: 1 }}
            control={control}
            as={Autocomplete<PassionSurvey<string>, string>}
            name="passion"
            label={t('declaration.passionSurvey.yourPassion.label')}
            data={passionOptions}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            shouldHighlightOnUntouched={Input.defaultHighlightCheck}
            onChange={onPassionChange}
          />
          <Input
            style={{ flex: 1 }}
            control={control}
            as={TextField}
            name="otherPassion"
            label={t('declaration.passionSurvey.otherPassion.label')}
            placeholder={t(
              'declaration.passionSurvey.otherPassion.placeholder',
            )}
            editable={showOtherPassion}
            disabled={!showOtherPassion}
            error={
              showOtherPassion
                ? (errors.otherPassion?.message as string)
                : undefined
            }
            shouldHighlightOnUntouched={showOtherPassion}
          />
        </Row>
      </Box>
    </ApplicationDetailsTabletSectionContainer>
  );
}
