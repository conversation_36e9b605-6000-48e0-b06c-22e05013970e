import { useCheckProductionEnv } from 'features/customerFactFind/hooks/useCheckProductionEnv';
import DocumentsSummaryBase from 'features/eAppV2/common/components/review/documentSummary/DocumentsSummaryBase';
import { DocumentReview } from 'features/eAppV2/common/types/reviewTypes';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Party, PartyRole } from 'types/party';

export default function DocumentsSummary() {
  const { t } = useTranslation(['eApp']);
  const isPrd = useCheckProductionEnv();

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const data = useMemo<DocumentReview[]>(() => {
    const owner = caseObj?.parties?.find(p =>
      p.roles.includes(PartyRole.PROPOSER),
    );
    const insured = caseObj?.parties?.find(
      p =>
        p.roles.includes(PartyRole.INSURED) &&
        !p.roles.includes(PartyRole.PROPOSER),
    );

    const payor = isPrd
      ? null
      : caseObj?.parties?.find(
          p => p.roles.includes(PartyRole.PAYER) && Boolean(caseObj.havePayer),
        );

    const renewalPayer = caseObj?.parties?.find(
      p => p.roles.includes(PartyRole.RENEWAL_PAYER) && p?.id !== owner?.id,
    );

    return (
      [owner, insured, payor, renewalPayer]
        .filter((p): p is Party => Boolean(p))
        .map(party => {
          const files =
            caseObj?.files?.filter(file => file?.partyId === party.id) || [];
          return {
            name: party.person?.name?.fullName ?? (party.entity?.name || ''),
            documents: Object.values(
              files.reduce<Record<string, DocumentReview['documents'][0]>>(
                (documents, file) => {
                  if (!documents[file.docType]) {
                    documents[file.docType] = {
                      // @ts-expect-error missing translation key
                      type: t(`eApp:documentUpload.${file.docType}`),
                      images: [],
                    };
                  }
                  documents[file.docType].images.push(file.fileName);
                  return documents;
                },
                {},
              ),
            ),
          };
        }) || []
    );
  }, [caseObj?.files, caseObj?.parties, t]);

  return <DocumentsSummaryBase data={data} displayStyle="doc-type" />;
}
