import { OcrRef } from 'components/Ocr/Ocr';
import { useMemo, useRef, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { OcrResult } from 'types/ocr';
import { MINOR_AGE_THRESHOLD } from '../constants/partyConfig';
import { calculateAge } from 'utils/helper/calculateAge';
import { PartyRole } from 'types/party';
import { ID_COUNTRY } from 'constants/optionList';
import { UseFormGetValues, UseFormSetValue } from 'react-hook-form';
import {
  MismatchFields,
  OcrValidationResult,
  validateOcr,
} from 'features/eAppV2/common/utils/validateOcr';
import { Gender } from 'types/person';
import { RootStackParamList } from 'types';
import OcrValidationErrorDialog from 'features/eAppV2/common/components/ocr/OcrValidationErrorDialog';
import OcrValidationErrorDialogButton from 'features/eAppV2/common/components/ocr/OcrValidationErrorDialogButton';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import useBoundStore from 'hooks/useBoundStore';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { LargeLabel } from 'cube-ui-components';
import useOcrPopulatingLogic from './useOcrPopulatingLogic';
import { MainPartyPersonalDetailsForm } from '../validations/applicationDetails/sections/mainPartyPersonalDetails';
import { MainPartyAddressInformationForm } from '../validations/applicationDetails/sections/addressInformation';

export type OCRForm = Pick<
  MainPartyPersonalDetailsForm,
  | 'primaryId'
  | 'maritalStatus'
  | 'fullName'
  | 'dob'
  | 'gender'
  | 'placeOfBirth'
  | 'nationality'
  | 'religion'
> &
  Pick<
    MainPartyAddressInformationForm,
    | 'correspondenceAddressLine1'
    | 'correspondenceAddressLine2'
    | 'correspondenceAddressLine3'
    | 'correspondenceAddressLine4'
  >;

type OcrFile = {
  base64: string;
  name: string;
  thumbnail?: string;
};

export interface OcrLogicProps {
  role:
    | PartyRole.PROPOSER
    | PartyRole.INSURED
    | PartyRole.PAYER
    | PartyRole.RENEWAL_PAYER;
  setValue: UseFormSetValue<OCRForm>;
  getValues: UseFormGetValues<OCRForm>;
  ocrImage: {
    base64?: string;
    name?: string;
    thumbnail?: string;
  };
  isPIEqualPO?: boolean;
  onClose?: () => void;
}

const useOcrLogic = ({
  role,
  setValue,
  getValues,
  ocrImage,
  isPIEqualPO,
  onClose,
}: OcrLogicProps) => {
  const { t } = useTranslation(['eApp']);
  const ocrRef = useRef<OcrRef>(null);
  const { space, colors } = useTheme();
  const [ocrFile, setOcrFile] = useState<OcrFile>({
    base64: '',
    name: '',
    thumbnail: '',
  });
  const [isOcrSuccess, setIsOcrSuccess] = useState(false);
  const [ocrValidationMismatchFields, setOcrValidationMismatchFields] =
    useState<MismatchFields>({});

  const dob = getValues('dob');
  const nationality = getValues('nationality');

  const age = calculateAge(dob ?? new Date());

  const [ocrValidationResult, setOcrValidationResult] =
    useState<OcrValidationResult>(OcrValidationResult.Match);

  const ocrScanned = useRef(Boolean(ocrImage?.name));

  const { populateOcrData } = useOcrPopulatingLogic(
    role,
    ocrImage,
    setValue,
    getValues,
  );

  const { reset } = useNavigation<NavigationProp<RootStackParamList>>();
  const clearActiveCase = useBoundStore(
    state => state.caseActions.clearActiveCase,
  );

  const [ocrValidationErrorDialogVisible, setOcrValidationErrorDialogVisible] =
    useState(false);

  const isShowCreateNewQuoteButtonForValidationErrorDialog =
    ((isPIEqualPO && role === PartyRole.PROPOSER) ||
      role === PartyRole.INSURED) &&
    ocrValidationResult === OcrValidationResult.DobOrGenderMismatch;

  const onRetakeForValidationErrorDialog = () => {
    ocrRef?.current?.resetAndOpen();
    setOcrFile({
      base64: '',
      name: '',
      thumbnail: '',
    });
    setOcrValidationErrorDialogVisible(false);
  };

  const onSkipForValidationErrorDialog = async () => {
    ocrRef?.current?.reset();
    setOcrValidationErrorDialogVisible(false);
  };
  const renderValidationDialog = () => (
    <OcrValidationErrorDialog
      visible={ocrValidationErrorDialogVisible}
      fields={ocrValidationMismatchFields}>
      <OcrValidationErrorDialogButton
        text={t('eApp:ocr.error.retake')}
        onPress={onRetakeForValidationErrorDialog}
      />
      <OcrValidationErrorDialogButton
        text={t('eApp:ocr.error.skip')}
        style={{
          marginTop: space[3],
        }}
        variant="text"
        onPress={onSkipForValidationErrorDialog}
      />
      {isShowCreateNewQuoteButtonForValidationErrorDialog && (
        <TouchableOpacity
          onPress={() => {
            setOcrValidationErrorDialogVisible(false);
            onClose?.();
            clearActiveCase();
            reset({
              index: 0,
              routes: [{ name: 'Main' }, { name: 'CoverageDetailsScreen' }],
            });
          }}>
          <LargeLabel
            style={{ marginTop: space[6] }}
            fontWeight="bold"
            color={colors.primary}>
            {t('eApp:ocr.error.newApplication')}
          </LargeLabel>
        </TouchableOpacity>
      )}
    </OcrValidationErrorDialog>
  );
  const onFinish = async (
    data: OcrResult['extract'],
    documentType: string,
    image: {
      base64: string;
      name: string;
      thumbnail?: string;
    },
  ) => {
    ocrScanned.current = true;
    setOcrFile({
      base64: image.base64,
      name: image.name,
      thumbnail: image.thumbnail,
    });
    if (role === PartyRole.PROPOSER || role === PartyRole.INSURED) {
      const [result, mismatchFields] = validateOcr(
        {
          firstName: '',
          lastName: '',
          fullName: data.fullName,
          dateOfBirth: data.dateOfBirth,
          gender: data.gender,
        },
        {
          firstName: '',
          lastName: '',
          fullName: getValues('fullName'),
          dateOfBirth: getValues('dob') ?? null,
          gender: getValues('gender') as Gender,
        },
      );

      if (result === OcrValidationResult.Match) {
        setIsOcrSuccess(true);
        populateOcrData({ data, documentType, image });
      } else {
        setIsOcrSuccess(false);
        setOcrValidationResult(result);
        setOcrValidationMismatchFields(mismatchFields);
        setOcrValidationErrorDialogVisible(true);
      }
    } else {
      populateOcrData({ data, documentType, image });
    }
  };

  const isOCREnabled = useMemo(() => {
    if (nationality !== ID_COUNTRY) return false;
    return age >= MINOR_AGE_THRESHOLD;
  }, [age, nationality]);

  return {
    ocrLogic: {
      ocrImage,
      ocrScanned,
      onFinish,
      ocrRef,
      isOcrSuccess,
      isVerifyingCustomer: false,
      renderConfirmationDialog: renderValidationDialog,
      renderLADialog: () => <View />,
      renderValidationDialog: () => <View />,
      ocrValidationMismatchFields: ocrValidationMismatchFields,
    },
    isOCREnabled,
    ocrFile,
  };
};

export default useOcrLogic;
