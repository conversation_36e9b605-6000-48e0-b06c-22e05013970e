import React, { useEffect, useState } from 'react';
import { Platform } from 'react-native';
import {
  ActionPanel,
  Column,
  DatePicker,
  H6,
  Picker,
  Row,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import { checkCustomDuration } from 'features/reportGeneration/ph/ToolbarProvider';
import { format } from 'date-fns';
import { DatePeriodSearch, DatePeriodType } from 'types/report';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import {
  CommonModal,
  ModalFormAction,
} from 'features/reportGeneration/ph/components/CommonModal';
import DatePickerCalendar from 'components/DatePickerCalendar';
import { dateFormatUtil } from 'utils/helper/formatUtil';

/**
 *  For both mobile and tablet
 *  Issue date / Due date
 */
export default function DatePeriodActionPanel({
  visible,
  handleClose,
  contextValue,
  updateContextValue,
  defaultDatePeriod,
  disableTypeChip = false,
}: {
  visible: boolean;
  handleClose: () => void;
  contextValue: DatePeriodSearch;
  updateContextValue: (contextValue: DatePeriodSearch) => void;
  defaultDatePeriod: DatePeriodSearch;
  disableTypeChip?: boolean;
}) {
  const { t } = useTranslation('reportGeneration');
  const { space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { isTabletMode } = useLayoutAdoptionCheck();

  /**
   * 3 Separate states -
   * handling Android bug with Datepicker onChange
   * for UI display and saving state
   */
  const { datePeriodType, from, to } = contextValue;
  const [tempDatePeriodType, setTempDatePeriodType] = useState(datePeriodType);
  const [tempFrom, setTempFrom] = useState(from);
  const [tempTo, setTempTo] = useState(to);

  const DATE_PERIOD_CONFIG = [
    {
      label: 'Issue date',
      value: 'ISSUEDATE' as DatePeriodType,
    },
    {
      label: 'Due date',
      value: 'DUEDATE' as DatePeriodType,
    },
  ];

  const handleSelectedChipChange = (value: string) => {
    if (!value) return;
    if (value === 'ISSUEDATE') setTempDatePeriodType('ISSUEDATE');
    if (value === 'DUEDATE') setTempDatePeriodType('DUEDATE');
    return;
  };

  // Date validation states
  const [isCustomDurationValid, setIsCustomDurationValid] = useState(true);
  const [typeOnchange, setTypeOnchange] = useState<'from' | 'to' | ''>('');
  const [dateFromOnError, setDateFromOnError] = useState(false);
  const [dateToOnError, setDateToOnError] = useState(false);

  // Check if custom duration is valid
  useEffect(() => {
    const isValid = checkCustomDuration(tempFrom, tempTo);
    setIsCustomDurationValid(isValid);
  }, [tempFrom, tempTo]);

  // Control date picker error state
  useEffect(() => {
    if (isCustomDurationValid) {
      setDateFromOnError(false);
      setDateToOnError(false);
    }
    if (!isCustomDurationValid) {
      if (typeOnchange === 'from') setDateFromOnError(true);
      if (typeOnchange === 'to') setDateToOnError(true);
    }
  }, [isCustomDurationValid, typeOnchange]);

  // Date picker onChange functions
  const datePickerFromOnchange = (date: Date) => {
    setTypeOnchange('from');
    setTempFrom(format(date, 'yyyy-MM-dd'));
  };

  const datePickerToOnchange = (date: Date) => {
    setTypeOnchange('to');
    setTempTo(format(date, 'yyyy-MM-dd'));
  };

  // Reset panel to context Value
  const resetPanelToContextValue = () => {
    setTempDatePeriodType(datePeriodType);
    setTempFrom(from);
    setTempTo(to);
  };

  // Reset panel to default value
  const resetPanelToDefaultValue = () => {
    setTempDatePeriodType(defaultDatePeriod?.datePeriodType);
    setTempFrom(defaultDatePeriod?.from);
    setTempTo(defaultDatePeriod?.to);
  };

  // Form action functions
  const formActionOnPrimaryPress = () => {
    updateContextValue({
      datePeriodType: tempDatePeriodType,
      from: tempFrom,
      to: tempTo,
    });
    handleClose();
  };

  const formActionOnSecondaryPress = () => {
    resetPanelToDefaultValue();
  };

  /**
   * Tablet mode: Modal
   */
  if (isTabletMode)
    return (
      <CommonModal
        visible={visible}
        onClose={() => {
          resetPanelToContextValue();
          handleClose();
        }}>
        <Column gap={space[6]}>
          <H6 fontWeight="bold" children={t('actionPanel.title.datePeriod')} />
          {!disableTypeChip && (
            <Picker
              type="chip"
              disabled={disableTypeChip}
              size="large"
              items={DATE_PERIOD_CONFIG}
              value={tempDatePeriodType}
              onChange={value => handleSelectedChipChange(value)}
            />
          )}

          <Row gap={space[5]}>
            <DatePickerCalendar
              label={t('actionPanel.from')}
              value={new Date(tempFrom)}
              formatDate={date => dateFormatUtil(date)}
              onChange={date => datePickerFromOnchange(date)}
              hint="MM/DD/YYYY"
              error={
                dateFromOnError ? t('actionPanel.error.dateFrom') : undefined
              }
              style={{ flex: 1 }}
            />

            <DatePickerCalendar
              label={t('actionPanel.to')}
              value={new Date(tempTo)}
              formatDate={date => dateFormatUtil(date)}
              onChange={date => datePickerToOnchange(date)}
              hint="MM/DD/YYYY"
              error={dateToOnError ? t('actionPanel.error.dateTo') : undefined}
              style={{ flex: 1 }}
            />
          </Row>
        </Column>

        <ModalFormAction
          hasShadow={false}
          primaryLabel={t('actionPanel.confirm')}
          onPrimaryPress={() => formActionOnPrimaryPress()}
          primaryDisabled={!isCustomDurationValid}
          secondaryLabel={t('actionPanel.reset')}
          onSecondaryPress={() => formActionOnSecondaryPress()}
          style={{
            paddingTop: space[10],
            paddingBottom: 0,
          }}
        />
      </CommonModal>
    );

  /**
   * Mobile mode: BottomSheet
   */
  return (
    <ActionPanel
      visible={visible}
      handleClose={() => {
        resetPanelToContextValue();
        handleClose();
      }}
      title={t('actionPanel.title.datePeriod')}
      contentContainerStyle={{
        padding: 0,
        paddingBottom: Platform.select({
          android: space[4] + bottom,
          ios: 0,
        }),
      }}>
      <Column py={space[4]} px={space[4]} gap={space[8]}>
        <Picker
          type="chip"
          size="medium"
          items={DATE_PERIOD_CONFIG}
          value={tempDatePeriodType}
          onChange={value => handleSelectedChipChange(value)}
        />

        <DatePicker
          label={t('actionPanel.from')}
          value={new Date(tempFrom)}
          formatDate={date => dateFormatUtil(date)}
          onChange={date => datePickerFromOnchange(date)}
          modalTitle={t('actionPanel.from')}
          hint="MM/DD/YYYY"
          error={dateFromOnError ? t('actionPanel.error.dateFrom') : undefined}
        />

        <DatePicker
          label={t('actionPanel.to')}
          value={new Date(tempTo)}
          formatDate={date => dateFormatUtil(date)}
          onChange={date => datePickerToOnchange(date)}
          modalTitle={t('actionPanel.to')}
          hint="MM/DD/YYYY"
          error={dateToOnError ? t('actionPanel.error.dateTo') : undefined}
        />
      </Column>

      <FormAction
        hasShadow={false}
        primaryLabel={t('actionPanel.confirm')}
        onPrimaryPress={() => formActionOnPrimaryPress()}
        primaryDisabled={!isCustomDurationValid}
        secondaryLabel={t('actionPanel.reset')}
        onSecondaryPress={() => formActionOnSecondaryPress()}
      />
    </ActionPanel>
  );
}
