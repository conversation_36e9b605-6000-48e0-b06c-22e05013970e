import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  Checkbox,
  Chip,
  Column,
  CubePictogramIcon,
  H6,
  Icon,
  Label,
  LargeBody,
  Row,
  SmallBody,
  SmallLabel,
  TextField,
  Typography,
} from 'cube-ui-components';
import useCheckIsLeader from 'features/reportGeneration/hooks/useCheckIsLeader';
import {
  MemberInfo,
  flattenTeamHierarchy,
} from 'features/reportGeneration/utils/reportUtils';
import { useGetTeamHierarchy } from 'hooks/useGetTeam';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import _ from 'lodash';
import useBoundStore from 'hooks/useBoundStore';
import SelectAgentModal from '../components/SelectAgentModal';
import { format, subDays, subMonths } from 'date-fns';
import { formatDate } from 'features/eRecruit/ib/phone/components/utils/FormatDate';
import DatePeriodActionPanel from 'features/reportGeneration/ph/components/ActionPanel/DatePeriodActionPanel';
import {
  endDayOfMonth,
  startDayOfMonth,
} from 'features/reportGeneration/ph/ToolbarProvider';
import { DatePeriodSearch } from 'types/report';
import MultipleSelectionList from '../components/MultipleSelectionList';
import { STATUS_LIST } from '../util/statusList';
import { useGetDueDateReport } from 'hooks/useReportGeneration';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';

const ICON_SIZE = 40;

const today = format(new Date(), 'yyyy-MM-dd');
const lastTwoMonths = format(subMonths(new Date(), 2), 'yyyy-MM-dd');
const lastThirtyDays = format(subDays(new Date(), 29), 'yyyy-MM-dd'); // 29 as today is included
const last180Days = format(subDays(new Date(), 179), 'yyyy-MM-dd'); // 179 as today is included
const DEFAULT_DATE_PERIOD: DatePeriodSearch = {
  datePeriodType: 'DUEDATE',
  from: lastTwoMonths,
  to: today,
};

export default function ReportGenerationMY() {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { colors, sizes, space } = useTheme();
  const { top } = useSafeAreaInsets();
  const { t } = useTranslation('reportGeneration');
  const [ANPDatePeriodButton, setANPDatePeriodButton] = useState('last2months');
  const [DDRDatePeriodButton, setDDRDatePeriodButton] = useState('last2months');
  const [ANPdatePeriodPanel, setANPDatePeriodPanel] = useState(false);
  const [DDRdatePeriodPanel, setDDRDatePeriodPanel] = useState(false);
  const [datePeriod, updateDatePeriod] =
    useState<DatePeriodSearch>(DEFAULT_DATE_PERIOD);
  const [applicationNotProceedDatePeriod, setApplicationNotProceedDatePeriod] =
    useState(DEFAULT_DATE_PERIOD);
  const [dueDateReportDatePeriod, setDueDateReportDatePeriod] =
    useState(DEFAULT_DATE_PERIOD);
  const [cirReportType, setCirReportType] = useState('general');
  const [selectedAgent, setSelectedAgent] = useState<MemberInfo | null>(null);
  const [showSelectedAgentList, setShowSelectedAgentList] = useState(false);
  const [showStatusSelectionList, setShowStatusSelectionList] = useState(false);
  const [isTeamSelected, setIsTeamSelected] = useState(false);
  const [selectedStatus, setSelectedStatus] =
    useState<{ code: string; meaning: string }[]>(STATUS_LIST);

  console.log('selectedStatus', selectedStatus);
  const onCloseShowSelectedAgentList = () => {
    setShowSelectedAgentList(false);
  };

  const onCloseShowStatusSelectionList = () => {
    setShowStatusSelectionList(false);
  };

  const { data: agentProfile } = useGetAgentProfile();
  const isLeader = agentProfile?.isLeader;
  const { data: teamData, isLoading: isTeamLoading } = useGetTeamHierarchy();
  const memberInfoList = flattenTeamHierarchy(teamData);
  const hasNoDownline =
    _.isEmpty(teamData?.members) && _.isEmpty(teamData?.subteams);

  const selfAgentCode = useBoundStore(state => state.auth.agentCode);

  useMemo(() => {
    setSelectedAgent(memberInfoList[0]);
  }, [teamData, isTeamSelected]);

  console.log('selectedAgent', selectedAgent);
  const handleSelectedAgent = (agent: MemberInfo) => {
    setSelectedAgent(agent);
  };

  const CARD_CONFIG = [
    {
      title: t('applicationNotProceed'),
      icon: <CubePictogramIcon.MedicalReportError size={ICON_SIZE} />,
    },
    {
      title: t('dueDateReport'),
      icon: <CubePictogramIcon.Timer size={ICON_SIZE} />,
    },
    {
      title: t('certificateTransactionReport'),
      icon: <CubePictogramIcon.CreditCardOnHand size={ICON_SIZE} />,
    },
    {
      title: t('certificateInquiriesReport'),
      icon: <CubePictogramIcon.InformationDocument size={ICON_SIZE} />,
    },
  ];

  const DATEPERIOD_CONFIG = [
    {
      title: t('last2months'),
      value: 'last2months',
    },
    {
      title: t('last30days'),
      value: 'last30days',
    },
    {
      title: t('last180days'),
      value: 'last180days',
    },
    {
      title: t('customise'),
      value: 'customise',
    },
  ];

  useMemo(() => {
    ANPDatePeriodButton === 'customise'
      ? setANPDatePeriodPanel(true)
      : setANPDatePeriodPanel(false);
    DDRDatePeriodButton === 'customise'
      ? setDDRDatePeriodPanel(true)
      : setDDRDatePeriodPanel(false);
  }, [ANPDatePeriodButton, DDRDatePeriodButton]);

  const handleSelectedStatus = ({
    selected,
  }: {
    selected: { code: string; meaning: string }[];
  }) => {
    setSelectedStatus(selected);
  };

  return (
    <>
      <DatePeriodActionPanel
        visible={ANPdatePeriodPanel}
        handleClose={() => setANPDatePeriodPanel(false)}
        contextValue={applicationNotProceedDatePeriod}
        updateContextValue={setApplicationNotProceedDatePeriod}
        defaultDatePeriod={DEFAULT_DATE_PERIOD}
        disableTypeChip={true}
      />
      <DatePeriodActionPanel
        visible={DDRdatePeriodPanel}
        handleClose={() => setDDRDatePeriodPanel(false)}
        contextValue={dueDateReportDatePeriod}
        updateContextValue={setDueDateReportDatePeriod}
        defaultDatePeriod={DEFAULT_DATE_PERIOD}
        disableTypeChip={true}
      />
      <SelectAgentModal
        visible={showSelectedAgentList}
        onClose={onCloseShowSelectedAgentList}
        data={memberInfoList}
        handleSelectAgent={handleSelectedAgent}
      />
      <MultipleSelectionList
        title={t('generalStatus')}
        visible={showStatusSelectionList}
        onClose={onCloseShowStatusSelectionList}
        data={STATUS_LIST}
        handleSelected={handleSelectedStatus}
      />
      <View style={{ paddingTop: top, backgroundColor: colors.background }} />

      <ScrollView
        bounces={false}
        showsVerticalScrollIndicator={false}
        style={{
          paddingHorizontal: space[8],
          backgroundColor: colors.palette.fwdGrey[50],
        }}
        contentContainerStyle={{
          gap: space[3],
        }}>
        <Row justifyContent="space-between" alignItems="center">
          <H6
            fontWeight="bold"
            children={t('reportGeneration')}
            style={{
              paddingTop: space[8],
              paddingBottom: space[7],
            }}
          />
          {isLeader && (
            <Row
              alignItems="center"
              justifyContent="space-between"
              gap={space[1]}>
              <TouchableOpacity
                disabled={isTeamSelected}
                onPress={() => {
                  setShowSelectedAgentList(true);
                }}>
                <Row
                  gap={space[2]}
                  paddingX={space[3]}
                  paddingY={space[2]}
                  backgroundColor={
                    isTeamSelected
                      ? colors.palette.fwdGrey[20]
                      : colors.background
                  }
                  borderRadius={sizes[2]}
                  borderWidth={1}
                  borderColor={colors.palette.fwdGrey[100]}>
                  <SmallBody
                    color={
                      isTeamSelected
                        ? colors.palette.fwdGrey[100]
                        : colors.onBackground
                    }>
                    Selected agent
                  </SmallBody>
                  <Row gap={space[1]} alignItems="center">
                    <Label
                      fontWeight="normal"
                      color={
                        isTeamSelected
                          ? colors.palette.fwdGrey[100]
                          : colors.onBackground
                      }>
                      {selectedAgent?.agentName ?? '--'}
                    </Label>
                    <Icon.ChevronDown
                      size={sizes[4]}
                      fill={
                        isTeamSelected
                          ? colors.palette.fwdGrey[100]
                          : colors.onBackground
                      }
                    />
                  </Row>
                </Row>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setIsTeamSelected(!isTeamSelected);
                }}>
                <Row
                  gap={space[2]}
                  paddingX={space[3]}
                  paddingY={space[2]}
                  backgroundColor={
                    isTeamSelected
                      ? colors.palette.fwdOrange[5]
                      : colors.background
                  }
                  borderRadius={sizes[2]}
                  borderWidth={isTeamSelected ? 2 : 1}
                  borderColor={
                    isTeamSelected
                      ? colors.primary
                      : colors.palette.fwdGrey[100]
                  }>
                  <Checkbox
                    value={isTeamSelected}
                    onChange={() => {
                      setIsTeamSelected(!isTeamSelected);
                    }}
                  />
                  <Row gap={space[1]} alignItems="center">
                    <Label fontWeight="normal">Team</Label>
                  </Row>
                </Row>
              </TouchableOpacity>
            </Row>
          )}
        </Row>
        <Row gap={space[3]}>
          {CARD_CONFIG.map((cardItem, index) => {
            if (index < 2) {
              return (
                <OverviewCard key={index}>
                  <Row gap={space[3]} alignItems="center">
                    {cardItem.icon}
                    <H6 fontWeight="bold" children={cardItem.title} />
                  </Row>
                  <Row gap={space[3]} alignItems="center">
                    <SmallLabel color={colors.palette.fwdGreyDarkest}>
                      {t('dateType')}
                    </SmallLabel>
                    <Chip
                      label={index == 0 ? t('registrationDate') : t('dueDate')}
                      focus={true}
                      onPress={() => {
                        console.log('pressed chip');
                      }}
                    />
                  </Row>
                  <Column gap={space[2]}>
                    <SmallLabel color={colors.palette.fwdGreyDarkest}>
                      {t('datePeriod')}
                    </SmallLabel>
                    <Row gap={space[2]}>
                      {DATEPERIOD_CONFIG.map((item, index) => {
                        return (
                          <Chip
                            key={index}
                            label={item.title}
                            focus={
                              cardItem.title === t('applicationNotProceed')
                                ? item.value === ANPDatePeriodButton
                                : item.value === DDRDatePeriodButton
                            }
                            onPress={() => {
                              cardItem.title === t('applicationNotProceed')
                                ? setANPDatePeriodButton(item.value)
                                : setDDRDatePeriodButton(item.value);
                              cardItem.title === t('applicationNotProceed')
                                ? setApplicationNotProceedDatePeriod({
                                    ...applicationNotProceedDatePeriod,
                                    from:
                                      item.value === 'last2months'
                                        ? lastTwoMonths
                                        : item.value === 'last30days'
                                        ? lastThirtyDays
                                        : item.value === 'last180days'
                                        ? last180Days
                                        : '',
                                    to: today,
                                  })
                                : setDueDateReportDatePeriod({
                                    ...dueDateReportDatePeriod,
                                    from:
                                      item.value === 'last2months'
                                        ? lastTwoMonths
                                        : item.value === 'last30days'
                                        ? lastThirtyDays
                                        : item.value === 'last180days'
                                        ? last180Days
                                        : '',
                                    to: today,
                                  });
                            }}
                          />
                        );
                      })}
                    </Row>
                  </Column>
                  <LargeBody>
                    {cardItem.title === t('applicationNotProceed')
                      ? `${formatDate(
                          applicationNotProceedDatePeriod.from,
                        )} to ${formatDate(applicationNotProceedDatePeriod.to)}`
                      : `${formatDate(
                          dueDateReportDatePeriod.from,
                        )} to ${formatDate(dueDateReportDatePeriod.to)}`}
                  </LargeBody>
                  <Box alignItems="flex-end">
                    <Button
                      variant="primary"
                      text={t('generate')}
                      size="default"
                      onPress={() => {
                        if (!selectedAgent) {
                          console.error('No agent selected');
                          return;
                        }
                        if (cardItem.title === t('applicationNotProceed')) {
                          console.log('pressed generate report');
                          navigation.navigate('ApplicationNotProceedScreen', {
                            to: applicationNotProceedDatePeriod.to,
                            from: applicationNotProceedDatePeriod.from,
                            agent: selectedAgent,
                            team: isTeamSelected,
                          });
                        }
                        if (cardItem.title === t('dueDateReport')) {
                          navigation.navigate('DueDateReportScreen', {
                            to: dueDateReportDatePeriod.to,
                            from: dueDateReportDatePeriod.from,
                            agent: selectedAgent,
                            team: isTeamSelected,
                          });
                        }
                      }}
                    />
                  </Box>
                </OverviewCard>
              );
            }
          })}
        </Row>
        <Row gap={space[3]}>
          {CARD_CONFIG.map((item, index) => {
            if (index >= 2) {
              return (
                <OverviewCard key={index}>
                  <Row gap={space[3]} alignItems="center">
                    {item.icon}
                    <H6 fontWeight="bold" children={item.title} />
                  </Row>
                  {index === 3 ? (
                    <>
                      <Row gap={space[3]} alignItems="center">
                        <SmallLabel color={colors.palette.fwdGreyDarkest}>
                          {t('reportType')}
                        </SmallLabel>
                        <Chip
                          label={t('general')}
                          focus={cirReportType === 'general'}
                          onPress={() => {
                            setCirReportType('general');
                            console.log('pressed chip');
                          }}
                        />
                        <Chip
                          label={t('BIRO')}
                          focus={cirReportType === 'BIRO'}
                          onPress={() => {
                            setCirReportType('BIRO');
                            console.log('pressed chip');
                          }}
                        />
                      </Row>
                      <Column gap={space[2]}>
                        <SmallLabel color={colors.palette.fwdGreyDarkest}>
                          {t('statusSelected')} (
                          {selectedStatus ? selectedStatus.length : 0})
                        </SmallLabel>
                        <ScrollView horizontal>
                          <Row
                            gap={space[2]}
                            alignItems="center"
                            paddingBottom={space[6]}>
                            {selectedStatus.length === STATUS_LIST.length ? (
                              <Chip
                                label={t('allStatus')}
                                focus={true}
                                onPress={() => {
                                  setShowStatusSelectionList(true);
                                }}
                              />
                            ) : (
                              selectedStatus.map((status, index) => (
                                <Chip
                                  key={index}
                                  label={status.meaning}
                                  focus={true}
                                  onPress={() => {
                                    setShowStatusSelectionList(true);
                                  }}
                                />
                              ))
                            )}
                            <TouchableOpacity
                              onPress={() => {
                                setShowStatusSelectionList(true);
                              }}>
                              <Icon.Create
                                size={sizes[5]}
                                fill={colors.primary}
                              />
                            </TouchableOpacity>
                          </Row>
                        </ScrollView>
                      </Column>
                      <Box alignItems="flex-end">
                        <Button
                          variant="primary"
                          text={t('generate')}
                          size="default"
                          onPress={() => {
                            if (!selectedAgent) {
                              console.error('No agent selected');
                              return;
                            }
                            navigation.navigate('InquiriesReportScreen', {
                              to: today,
                              from: lastTwoMonths,
                              agent: selectedAgent,
                              team: isTeamSelected,
                              status: selectedStatus,
                              biro: true,
                            });
                            console.log('pressed generate report');
                          }}
                        />
                      </Box>
                    </>
                  ) : (
                    <>
                      <Box gap={space[3]}>
                        <Typography.H8 color={colors.palette.fwdGreyDarkest}>
                          {t('certificateTransactionReportDetails')}
                        </Typography.H8>
                        <TextField
                          label={t('certificateNumber')}
                          placeholder={t('certificateNumber')}
                          hint={t('digitHint')}
                        />
                      </Box>
                      <Box alignItems="flex-end">
                        <Button
                          disabled={true}
                          variant="primary"
                          text={t('generate')}
                          size="default"
                          onPress={() => {
                            console.log('pressed generate report');
                          }}
                        />
                      </Box>
                    </>
                  )}
                </OverviewCard>
              );
            }
          })}
        </Row>
      </ScrollView>
    </>
  );
}

const OverviewCard = styled.View(({ theme }) => {
  const { colors, space, sizes } = theme;
  return {
    flex: 1,
    backgroundColor: colors.background,
    borderRadius: sizes[4],
    padding: space[4],
    gap: space[3],
  };
});
