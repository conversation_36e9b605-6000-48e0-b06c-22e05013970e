import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { useHeaderHeight } from '@react-navigation/elements';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { FlashList } from '@shopify/flash-list';
import ResponsiveView from 'components/ResponsiveView';
import { Body, Box, Column, LoadingIndicator, Row } from 'cube-ui-components';
import POSListDrawer from 'features/policy/components/POSScreen/phone/POSListDrawer';
import { SortDirectionKeys } from 'features/savedProposals/types';
import useBoundStore from 'hooks/useBoundStore';
import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  RefreshControl,
} from 'react-native';
import Animated, {
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { PosPramList } from 'types/navigation';
import { PosPolicyDetail, POSStatus } from 'types/policy';
import CountAndSortHeader from './components/CountAndSortHeader';
import { sortDateHandler } from 'utils/helper/dateUtil';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { useGetPosPolicyByAgentId } from 'hooks/useGetPolicyList';
import { Placeholder } from './components/PlaceHolder';
import { POSItem } from 'features/policy/components/POSScreen/phone/POSItem';
import { dateFormatUtil } from 'utils/helper/formatUtil';

export type POSProps = NativeStackScreenProps<PosPramList, 'POSList'>;

export default function PolicyServicingListScreen() {
  const route = useRoute<POSProps['route']>();
  const { status, viewingAgentCode } = route.params;

  const { t } = useTranslation('policy');
  const { colors, space, sizes, animation } = useTheme();
  const navigation = useNavigation<NavigationProp<PosPramList>>();
  const loginAgentCode = useBoundStore(state => state.auth.agentCode);

  /**
   * Drawer tab state
   */
  const [activeStatus, setActiveStatus] = useState<POSStatus>(status);

  /**
   * Display data range
   */
  const DAYS = {
    due: '30',
    overdue: '45',
    freelook: '90',
  } as const;

  /**
   * Sorting
   */
  const [sortOrder, setSortOrder] = useState<SortDirectionKeys>('newest');

  /**
   * Due, Overdue, Freelook cancellation
   */
  // const isLoading = false;
  // const data = getPOSDummyData();
  const { data, isLoading, isRefetching, refetch } = useGetPosPolicyByAgentId(
    loginAgentCode || '',
  );

  const listData =
    activeStatus == 'due'
      ? data?.dues?.sort((a, b) =>
          sortDateHandler({
            aDate: a?.dueDate,
            bDate: b?.dueDate,
            sortOrder: sortOrder,
          }),
        )
      : activeStatus == 'overdue'
      ? data?.overdues?.sort((a, b) =>
          sortDateHandler({
            aDate: a?.dueDate,
            bDate: b?.dueDate,
            sortOrder: sortOrder,
          }),
        )
      : activeStatus == 'freelook'
      ? data?.freeLooks?.sort((a, b) =>
          sortDateHandler({
            aDate: a?.freeLookDate,
            bDate: b?.freeLookDate,
            sortOrder: sortOrder,
          }),
        )
      : [];

  /**
   * Animation
   */
  const scrollY = useSharedValue(0);
  const expanded = useSharedValue(true);
  const scrollRef = useRef<FlashList<[]> | null>(null);
  const headerHeight = useHeaderHeight();
  const windowHeight = Dimensions.get('window').height;
  const animatedStyle = useAnimatedStyle(() => {
    if (expanded.value === true) {
      return {
        maxHeight: withTiming(windowHeight, {
          duration: animation.duration * 0.2,
        }),
        top: withTiming(0, {
          duration: animation.duration * 0.2,
        }),
        opacity: withTiming(1, {
          duration: animation.duration * 1.2,
        }),
      };
    }
    return {
      maxHeight: withTiming(0, {
        duration: animation.duration * 0.2,
      }),

      top: withTiming(-headerHeight * 2, {
        duration: animation.duration * 0.2,
      }),
      opacity: withTiming(0, { duration: animation.duration * 0.75 }),
    };
  });

  const scrollToTop = () => {
    scrollRef.current?.scrollToOffset({ animated: true, offset: 0 });
    expanded.value = true;
  };

  const handleScrollStart = (
    event: NativeSyntheticEvent<NativeScrollEvent>,
  ) => {
    if (scrollY) {
      scrollY.value = event.nativeEvent.contentOffset.y;
    }
  };

  const handleScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    if (scrollY) {
      if (event.nativeEvent.contentOffset.y <= 0) expanded.value = true;
      else if (scrollY.value < event.nativeEvent.contentOffset.y)
        expanded.value = false;
      else expanded.value = true;
      scrollY.value = event.nativeEvent.contentOffset.y;
    }
  };

  return (
    <Container>
      <POSListDrawer
        expanded={expanded}
        scrollToTop={() => scrollToTop()}
        routeStatus={activeStatus}
        onChange={status => {
          setActiveStatus(status);
          setSortOrder('newest');
        }}
      />

      <ListContainer>
        <Animated.View layout={LinearTransition} style={animatedStyle}>
          <Column pl={space[2]} py={space[4]} gap={space[2]}>
            <CountAndSortHeader
              listDataCount={listData?.length ?? 0}
              order={sortOrder}
              onPress={() =>
                setSortOrder(sortOrder === 'newest' ? 'oldest' : 'newest')
              }
            />
            <Body
              children={t('displayingDataFromLastDays', {
                days: DAYS[activeStatus],
              })}
              color={colors.palette.fwdGreyDarkest}
            />
          </Column>
        </Animated.View>

        {/* ListComponent */}
        <FlashList
          refreshControl={
            <RefreshControl refreshing={isRefetching} onRefresh={refetch} />
          }
          data={listData}
          renderItem={({ item }) => {
            if (activeStatus === 'due') {
              return (
                <POSItem.DueItem
                  type="default"
                  item={item as unknown as PosPolicyDetail}
                  disabled={false}
                  onPress={() => {
                    navigation.navigate('POSDetail', {
                      type: 'policy',
                      policyId: item?.policyNo,
                      status: 'due',
                      policyInfo: {
                        dueDate: item?.dueDate
                          ? dateFormatUtil(new Date(item.dueDate))
                          : '--',
                      },
                    });
                  }}
                />
              );
            }
            if (activeStatus === 'overdue') {
              return (
                <POSItem.OverdueItem
                  type="default"
                  item={item as unknown as PosPolicyDetail}
                  disabled={false}
                  onPress={() => {
                    navigation.navigate('POSDetail', {
                      type: 'policy',
                      policyId: item?.policyNo,
                      status: 'overdue',
                      policyInfo: {
                        dueDate: item?.dueDate
                          ? dateFormatUtil(new Date(item.dueDate))
                          : '--',
                      },
                    });
                  }}
                />
              );
            }
            if (activeStatus === 'freelook') {
              return (
                <POSItem.FreeLookItem
                  type="default"
                  item={item as unknown as PosPolicyDetail}
                  disabled={false}
                  onPress={() => {
                    navigation.navigate('POSDetail', {
                      type: 'policy',
                      policyId: item?.policyNo,
                      status: 'freelook',
                      policyInfo: {
                        issueDate: item?.freeLookDate
                          ? dateFormatUtil(new Date(item.freeLookDate))
                          : '--',
                      },
                    });
                  }}
                />
              );
            }
            return <></>;
          }}
          estimatedItemSize={127}
          ListEmptyComponent={
            isLoading ? (
              <Row justifyContent="center">
                <Box h={sizes[6]} w={sizes[6]}>
                  <LoadingIndicator />
                </Box>
              </Row>
            ) : (
              <Placeholder.EmptyRecord />
            )
          }
          onScrollBeginDrag={handleScrollStart}
          onScrollEndDrag={handleScrollEnd}
          ItemSeparatorComponent={() => <Row paddingBottom={space[2]} />}
        />
      </ListContainer>
    </Container>
  );
}

const Container = styled(ResponsiveView)(({ theme }) => ({
  flexDirection: 'row',
  display: 'flex',
  height: '100%',
  paddingRight: theme.space[4],
}));

const ListContainer = styled(ResponsiveView)(() => ({
  flex: 1,
}));
