import React from 'react';
import { useTheme } from '@emotion/react';
import { Column, Row } from 'cube-ui-components';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import styled from '@emotion/native';
import TeamPanelLayout from './teamPanel/TeamPanelLayout';
import IndividualTeamDetailsLayout from './individualTeamDetails/IndividualTeamDetailsLayout';

export default function TeamManagementPH() {
  const { top } = useSafeAreaInsets();
  const { colors } = useTheme();

  return (
    <Row flex={1} pt={top} backgroundColor={colors.background}>
      <TeamPanelContainer>
        <TeamPanelLayout />
      </TeamPanelContainer>

      <IndividualPanelContainer>
        <IndividualTeamDetailsLayout />
      </IndividualPanelContainer>
    </Row>
  );
}

const TeamPanelContainer = styled.ScrollView(({ theme }) => ({
  flex: 1,
  paddingTop: theme.space[6],
  paddingLeft: theme.space[8],
  paddingRight: theme.space[4],
  backgroundColor: theme.colors.palette.fwdGrey[50],
}));

const IndividualPanelContainer = styled(Column)(({ theme }) => ({
  flex: 1,
  paddingTop: theme.space[6],
  paddingHorizontal: theme.space[4],
  backgroundColor: theme.colors.background,
}));
