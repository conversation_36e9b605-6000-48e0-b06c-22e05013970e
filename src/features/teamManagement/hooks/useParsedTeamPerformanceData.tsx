import { ProcPerformanceResponse } from 'hooks/useGetPerformance';

import {
  ProcPerformanceTargetResponse,
  TeamIndividualPerformanceResponse,
} from 'types/performance';
import {
  useGetTeamIndividualPerformanceByAgentId,
  useGetTeamIndividualTargetByAgentId,
} from 'hooks/useGetTeam';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { ParsedTeamPerformanceData } from '../Utils/teamIndividualProfileUtils';
import useSelectSalesData from './useSelectSalesData';

export default function useParsedTeamPerformanceData(agentCode: string) {
  const { data: salesData, isLoading: isSalesLoading } =
    useSelectSalesData(agentCode);

  const { data: targetData, isLoading: isTargetLoading } =
    useGetTeamIndividualTargetByAgentId(agentCode);

  const { isLoading: isIndividualPerformanceLoading, data: lineChartData } =
    useGetTeamIndividualPerformanceByAgentId(agentCode);

  return {
    isLoading:
      isSalesLoading || isIndividualPerformanceLoading || isTargetLoading,
    data: parsingPerformanceData(lineChartData, targetData, {
      ...salesData,
      agentId: salesData?.agentCode,
    }),
  };
}

export const parsingPerformanceData = (
  allPerformanceData: ProcPerformanceResponse | undefined,
  targetData: ProcPerformanceTargetResponse | undefined,
  teamIndividualData: TeamIndividualPerformanceResponse | undefined,
): ParsedTeamPerformanceData => {
  const currentMonth = new Date().getMonth() + 1;
  // get the current month (0-11)

  const currentMonthTargetData = targetData?.monthTarget?.find(
    ({ month }) => month === currentMonth?.toString(),
  );

  return {
    ytd: {
      apeAsOfDate: new Date(),
      apeCompletion:
        teamIndividualData?.individualPerformance?.ytd?.issuedApe || 0,
      apeSubmission:
        teamIndividualData?.individualPerformance?.ytd?.submittedApe || 0,
      apeTarget: targetData?.targetACE || 0,

      caseAsOfDate: new Date(),
      caseSubmission:
        teamIndividualData?.individualPerformance?.ytd?.submittedCase || 0,
      caseCompletion:
        teamIndividualData?.individualPerformance?.ytd?.issuedCase || 0,
      caseTarget: targetData?.targetCASE || 0,

      currency: allPerformanceData?.ytd?.currency || 'RM',

      apeSubmissionList: (allPerformanceData?.ytd?.apeSubmissionList ?? []).map(
        item => ({
          agentCode: item?.agentCode || '',
          ape: item?.apeSub || 0,
          mm: item?.mm || '',
          yyyy: item?.yyyy || '',
        }),
      ),
      apeCompletionList: (allPerformanceData?.ytd?.apeCompletionList ?? []).map(
        item => ({
          agentCode: item?.agentCode || '',
          ape: item?.ape || 0,
          mm: item?.mm || '',
          yyyy: item?.yyyy || '',
        }),
      ),
      caseCompletionList: allPerformanceData?.ytd?.caseCompletionList ?? [],
      caseSubmissionList: allPerformanceData?.ytd?.caseSubmissionList ?? [],
    },
    mtd: {
      apeAsOfDate: new Date(),
      apeSubmission:
        teamIndividualData?.individualPerformance?.mtd?.submittedApe || 0,
      apeCompletion:
        teamIndividualData?.individualPerformance?.mtd?.issuedApe || 0,
      apeTarget: currentMonthTargetData?.targetACE || 0,

      caseAsOfDate: new Date(),
      caseSubmission:
        teamIndividualData?.individualPerformance?.mtd?.submittedCase || 0,
      caseCompletion:
        teamIndividualData?.individualPerformance?.mtd?.issuedCase || 0,
      caseTarget: currentMonthTargetData?.targetCASE || 0,
      currency: allPerformanceData?.mtd?.currency || 'RM',
    },
    kpi: {
      persistency:
        teamIndividualData?.individualPerformance?.persistency?.firstYear || 0,
      secondYearPersistency:
        teamIndividualData?.individualPerformance?.persistency?.secondYear || 0,
      currentYearPersistency:
        teamIndividualData?.individualPerformance?.persistency?.firstYear || 0,
      asOfDate: dateFormatUtil(new Date()),
    },
  };
};
