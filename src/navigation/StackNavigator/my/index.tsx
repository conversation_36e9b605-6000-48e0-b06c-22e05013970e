import React, { useEffect, useRef, useState } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Modal, PanResponder, TouchableOpacity, View } from 'react-native';
import { useTheme } from '@emotion/react';
import useBoundStore from 'hooks/useBoundStore';
import HeaderCloseButton from 'navigation/components/HeaderCloseButton';
import useCheckIsLoggedIn from 'hooks/useCheckIsLoggedIn';

// * Types
// import { RootStackParamList, RootStackParamListMap } from 'types/navigation';
import { RootStackParamList, RootStackParamListMap } from 'types/navigation';
import { TeamPerformanceViewType } from 'types/team';

//* Screens
import LoginScreen from 'screens/LoginScreen';
import SettingScreen from 'screens/AgentProfileScreen/SettingScreen';
import AgentProfileScreen from 'screens/AgentProfileScreen/AgentProfileScreen';
import PersonalDetailsScreen from 'screens/AgentProfileScreen/PersonalDetailsScreen';
import ProposalTableScreen from 'screens/ProposalTableScreen';
import SalesIllustrationForm from 'screens/SalesIllustrationForm';
import ProductSelection from 'screens/ProductSelection';
import PdfViewerScreen from 'screens/PdfViewerScreen';
import CoverageDetailsScreen from 'screens/CoverageDetailsScreen';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';
import TeamTargetScreen from 'screens/TeamTargetScreen';
import TeamOperationScreen from 'screens/TeamOperationScreen';
import TeamPerformanceListScreen from 'screens/TeamPerformanceList';
import EAppScreen from 'screens/EAppScreen';
import ACRScreen from 'screens/ACRScreen/ACRScreen';
import TeamPerformanceDetailsScreen from 'screens/TeamPerformanceDetailsScreen';
import RPQQuestionFormScreen from 'screens/RpqQuestionFormScreen';
import RpqResultScreen from 'screens/RpqResultScreen';
import FundIllustrationForm from 'features/proposal/components/FundIllustrationForm/FundIllustrationForm';
import AgentPerformanceDetailsScreen from 'screens/AgentPerformanceDetailsScreen';
import PolicyDetailScreen from 'features/policy/components/PolicyDetails';
import PoliciesScreen from 'screens/PoliciesScreen/PoliciesScreen';
import ProductRecommendationScreen from 'screens/ProductRecommendationScreen';
// import ReportGenerationListScreen from 'screens/ReportGenerationListScreen.tsx';

//* ScreenLots
import { SimulationTableScreen } from 'screens/SimulationTableScreen';
import SellerExpScreens from 'screens/SellerExperienceScreens';
import FnaScreen from 'screens/FnaScreen';
import TeamMemberTargetsEditScreen from 'features/teamManagement/ph/phone/teamActivities/teamTarget/teamMemberTargetsEdit/TeamMemberTargetsEdit';

//* Navigator
import { BirthdayTasksScreen } from 'screens/TasksScreen';
import MainNavigator from 'navigation/MainNavigator/MainNavigator';
import BirthdayCardScreen from 'screens/BirthdayCardScreen';
import SavingsGoal from 'features/fna/components/goals/savings/SavingsGoal';
import ProtectionGoal from 'features/fna/components/goals/protection/ProtectionGoal';
import NotificationScreen from 'screens/NotificationScreen';
import CustomerFactFindScreen from 'screens/CustomerFactFindScreen';
import { countryModuleSellerConfig } from 'utils/config/module';
import { Box, Icon, Typography } from 'cube-ui-components';
import { formatSeconds } from 'utils/helper/formatUtil';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import AiBotScreen from 'screens/AiBotScreen';
import AiBotHistoryScreen from 'screens/AiBotScreen/AiBotHistoryScreen';
import AiBotFeedbackScreen from 'screens/AiBotScreen/AiBotFeedbackScreen';
import AiBotTableScreen from 'screens/AiBotScreen/AiBotTableScreen';
import { useForceLogout } from 'hooks/useForceLogout';
import ECoachHomePage from 'features/ecoach/screens/home/<USER>';
import SplashPage from 'features/ecoach/screens/Splash';
import UserProfilePage from 'features/ecoach/screens/UserProfile';
import SelectPolicyPage from 'features/ecoach/screens/selectPolicy/SelectPolicy';
import SelectDifficultyPage from 'features/ecoach/screens/SelectDifficulty';
import GuideLinesPage from 'features/ecoach/screens/guideLines/GuideLinesPage';
import VideoCallPage from 'features/ecoach/screens/call/VideoCallPage';
import SummaryPage from 'features/ecoach/screens/summary/Summary';
import SummaryTabletPage from 'features/ecoach/screens/summary/Summary.tablet';
import DetailSummaryPage from 'features/ecoach/screens/detailSummary/DetailSummary';
import DetailSummaryTabletPage from 'features/ecoach/screens/detailSummary/DetailSummary.tablet';
import SessionHistory from 'features/ecoach/screens/SessionHistory';
import OverallFeedback from 'features/ecoach/screens/OverallFeedback';
import WatchVideoPage from 'features/ecoach/screens/WatchVideoPage';
import SubmissionFailed from 'features/eAppV2/my/components/submission/SubmissionFailed';
import AiBotPromptLibraryScreen from 'screens/AiBotScreen/AiBotPromptLibraryScreen';
import DataPrivacyReview from 'features/eAppV2/ph/components/review/dataPrivacy/DataPrivacyReview';
import PolicyReplacementReview from 'features/eAppV2/ph/components/review/policyReplacement/PolicyReplacementReview';
import PersonalInformationReview from 'features/eAppV2/common/components/review/personalInformationReview/PersonalInformationReview';
import HealthQuestionsReview from 'features/eAppV2/common/components/review/healthQuestions/HealthQuestionsReview';
import FatcaReview from 'features/eAppV2/my/components/reviewSummary/declaration/FatcaReview';
import RocReview from 'features/eAppV2/my/components/reviewSummary/declaration/RocReview';
import PdpReview from 'features/eAppV2/my/components/reviewSummary/declaration/PdpReview';
import ImageListScreen from 'screens/ImageListScreen';
import Submission from 'features/eAppV2/my/components/submission/Submission';
import DueDateReportScreen from 'screens/ReportGenerationScreen/DueDateReportScreen';
import ApplicationNotProceedScreen from 'screens/ReportGenerationScreen/ApplicationNotProceedScreen';
import InquiriesReportScreen from 'screens/ReportGenerationScreen/InquiriesReportScreen';
import { useGetTabsMobile } from 'navigation/components/TabsConfig';
import { useHasPermission } from 'hooks/useCheckClientScope';
import { SocialMarketingCreateNewScreen } from 'screens/SocialMarketingCreateNewScreen';

const Stack = createNativeStackNavigator<RootStackParamListMap['my']>();
// const Stack = createNativeStackNavigator<RootStackParamList>();

export default function MYStackNavigator() {
  const accessToken = useBoundStore(state => state.auth.authInfo.accessToken);

  const hasPermission = useHasPermission();
  const timerId_forFiveMinCountdown = useRef<ReturnType<any>>(false);
  const timerId_forForceLogout = useRef<ReturnType<any>>(false);
  const [timeForInactivityInSecond, setTimeForInactivityInSecond] =
    useState(1500);
  const [logoutTime, setLogoutTime] = useState(300);
  const [isIdle, setIsIdle] = useState(false);

  const intervalLogoutTime = useRef<ReturnType<typeof setInterval>>();
  const countDownLogoutTime = () => setLogoutTime(prev => prev - 1);

  const { isTabletMode } = useLayoutAdoptionCheck();

  const { shouldTriggerLogout, forceLogout } = useForceLogout();

  const otherMenuTabsMobile = useGetTabsMobile().otherMenuTabs.filter(item => {
    if (!item.feature) {
      return true;
    }
    return hasPermission(item.feature);
  });

  useEffect(() => {
    if (isIdle) {
      intervalLogoutTime.current = setInterval(countDownLogoutTime, 1000);
      return () => clearInterval(intervalLogoutTime.current);
    }
  }, [isIdle]);

  useEffect(() => {
    if (logoutTime <= 0) {
      setLogoutTime(300);
      handleLogout();
    }
  }, [logoutTime]);

  useEffect(() => {
    resetInactivityTimeout();
  }, []);

  const handleLogout = async () => {
    await forceLogout();
    setIsIdle(false);
  };

  const panResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponderCapture: () => {
        setIsIdle(false);
        resetInactivityTimeout();
        return false;
      },
    }),
  ).current;

  const resetInactivityTimeout = () => {
    setLogoutTime(300);
    clearTimeout(timerId_forFiveMinCountdown.current);
    timerId_forFiveMinCountdown.current = setTimeout(() => {
      // action after user has been detected idle
      setIsIdle(true);
    }, timeForInactivityInSecond * 1000);
    clearTimeout(timerId_forForceLogout.current);
    timerId_forForceLogout.current = setTimeout(() => {
      // logout user after inactivity
      handleLogout();
      setTimeForInactivityInSecond(1500);
      setIsIdle(false);
    }, (timeForInactivityInSecond + 300) * 1000);
  };

  const { isLoggedIn } = useCheckIsLoggedIn();

  const { colors, space, borderRadius, typography } = useTheme();

  return (
    <>
      {isLoggedIn && isIdle && (
        <Modal
          animationType="fade"
          transparent={true}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Box
            flex={1}
            backgroundColor={'rgba(0, 0, 0, 0.5)'}
            alignItems="center"
            justifyContent="center">
            <View
              style={{
                backgroundColor: colors.primary,
                width: 480,
                borderTopLeftRadius: borderRadius.small,
                borderTopRightRadius: borderRadius.small,
                padding: space[2],
                paddingHorizontal: space[3],
              }}>
              <Typography.Body fontWeight="bold">
                Session Timeout
              </Typography.Body>
            </View>
            <View
              style={{
                maxHeight: 400,
                width: 480,
                padding: space[6],
                borderBottomLeftRadius: borderRadius.small,
                borderBottomRightRadius: borderRadius.small,
                backgroundColor: colors.background,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: space[1],
                }}>
                <Icon.Warning size={12} fill={colors.palette.black} />
                <Typography.LargeLabel>
                  Your online session will expire in
                </Typography.LargeLabel>
              </View>
              <View style={{ padding: space[5] }}>
                <Typography.H3 fontWeight="bold">
                  {formatSeconds(logoutTime)}
                </Typography.H3>
              </View>
              <Typography.LargeLabel>
                Please click "Continue" to keep working, or click "Log Off" to
                end your session now
              </Typography.LargeLabel>
              <View style={{ flexDirection: 'row', padding: space[5] }}>
                <TouchableOpacity
                  onPress={() => {
                    handleLogout();
                  }}
                  style={{
                    width: 120,
                    height: 42,
                    borderRadius: space[1],
                    borderWidth: 2,
                    borderColor: colors.primary,
                    paddingHorizontal: space[4],
                    paddingVertical: space[2],
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Typography.LargeLabel
                    fontWeight="bold"
                    color={colors.primary}>
                    Log Off
                  </Typography.LargeLabel>
                </TouchableOpacity>
                <Box boxSize={space[5]} />
                <TouchableOpacity
                  onPress={() => {
                    setIsIdle(false);
                    resetInactivityTimeout();
                  }}
                  style={{
                    width: 120,
                    height: 42,
                    borderRadius: space[1],
                    backgroundColor: colors.primary,
                    paddingHorizontal: space[4],
                    paddingVertical: space[2],
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Typography.LargeLabel
                    fontWeight="bold"
                    color={colors.background}>
                    Continue
                  </Typography.LargeLabel>
                </TouchableOpacity>
              </View>
            </View>
          </Box>
        </Modal>
      )}
      <View style={{ flex: 1 }} {...panResponder.panHandlers}>
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            animation: 'slide_from_right',
            headerTitleAlign: 'center',
          }}>
          {isLoggedIn ? (
            // * Authenticated Navigator
            <>
              <Stack.Screen name="Main">{() => <MainNavigator />}</Stack.Screen>
              <Stack.Screen
                name="NotificationScreen"
                component={NotificationScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Group>
                <Stack.Screen
                  name="FWDNews"
                  component={SellerExpScreens.FWDNewsScreen}
                  options={{
                    headerShown: false,
                    animation: 'fade',
                  }}
                />
                <Stack.Screen
                  name="FWDNewsDetails"
                  component={SellerExpScreens.FWDNewsDetailsScreen}
                  options={{
                    headerShown: false,
                    animation: 'fade',
                  }}
                />
                <Stack.Screen
                  name="FWDNewsBookmarks"
                  component={SellerExpScreens.FWDNewsBookmarkScreen}
                  options={{
                    headerShown: false,
                    animation: 'fade',
                  }}
                />
              </Stack.Group>

              {/* // Task Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="BirthdayTasksScreen"
                  component={BirthdayTasksScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // Birthday Card Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="BirthdayCardScreen"
                  component={BirthdayCardScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // Lead Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="LeadProfile"
                  component={SellerExpScreens.LeadProfileScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ProfileDetails"
                  component={SellerExpScreens.LeadProfileDetailsScreen}
                  options={{
                    animation: 'fade',
                  }}
                />
                <Stack.Screen
                  name="CustomerProfileDetails"
                  component={SellerExpScreens.CustomerProfileDetailsScreen}
                />
                <Stack.Screen
                  name="AddNewLeadOrEntity"
                  component={SellerExpScreens.AddNewLeadOrEntityScreen}
                  options={{
                    animation: 'fade',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ContactBook"
                  component={SellerExpScreens.ContactBookScreen}
                  options={{
                    animation: 'fade',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="LeadAndCustomerSearch"
                  component={SellerExpScreens.LeadAndCustomerSearch}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="LogActivity"
                  component={SellerExpScreens.LogActivityScreen}
                  options={{
                    animation: 'fade',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ExistingPolicyDetail"
                  component={PolicyDetailScreen}
                />
              </Stack.Group>

              <Stack.Group>
                <Stack.Screen
                  name="AiBotChat"
                  component={AiBotScreen}
                  options={{
                    headerShown: false,
                    animation: 'slide_from_bottom',
                  }}
                />
                <Stack.Screen
                  name="AiBotHistory"
                  component={AiBotHistoryScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="AiBotPromptLibrary"
                  component={AiBotPromptLibraryScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="AiBotFeedback"
                  component={AiBotFeedbackScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="AiBotTable"
                  component={AiBotTableScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // Ecoach Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="EcoachHome"
                  component={ECoachHomePage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="Splash"
                  component={SplashPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="UserProfile"
                  component={UserProfilePage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="SelectPolicy"
                  component={SelectPolicyPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="SelectDifficulty"
                  component={SelectDifficultyPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="GuideLinesPage"
                  component={GuideLinesPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="VideoCallPage"
                  component={VideoCallPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="Summary"
                  component={SummaryPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="SummaryTablet"
                  component={SummaryTabletPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="DetailSummary"
                  component={DetailSummaryPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="DetailSummaryTablet"
                  component={DetailSummaryTabletPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="SessionHistory"
                  component={SessionHistory}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="OverallFeedback"
                  component={OverallFeedback}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="WatchVideoPage"
                  component={WatchVideoPage}
                  options={{
                    headerShown: true,
                    title: '',
                    headerBackTitle: 'Home',
                  }}
                />
              </Stack.Group>

              {/* // Performance Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="RecognitionDetails"
                  component={SellerExpScreens.RecognitionDetailsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="PerformanceDetails"
                  component={SellerExpScreens.PerformanceDetailsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="PerformanceTarget"
                  component={SellerExpScreens.PerformanceTargetScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // Report Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="DueDateReportScreen"
                  component={DueDateReportScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ApplicationNotProceedScreen"
                  component={ApplicationNotProceedScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="InquiriesReportScreen"
                  component={InquiriesReportScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // FNA Screens */}

              {/* // SI Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="SalesIllustrationForm"
                  component={SalesIllustrationForm}
                />
                <Stack.Screen
                  name="RPQQuestionForm"
                  component={RPQQuestionFormScreen}
                />
                <Stack.Screen name="RPQResult" component={RpqResultScreen} />
                <Stack.Screen
                  name="ProductSelection"
                  component={ProductSelection}
                />
                <Stack.Screen
                  name="CoverageDetailsScreen"
                  component={CoverageDetailsScreen}
                />
              </Stack.Group>

              {/* // Agent Profile Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="AgentProfile"
                  component={AgentProfileScreen}
                />
                <Stack.Screen
                  name="PersonalDetails"
                  component={PersonalDetailsScreen}
                />
                <Stack.Screen name="Setting" component={SettingScreen} />
              </Stack.Group>

              {/* // Not Categorized */}
              <Stack.Group>
                <Stack.Screen
                  name="BadgesCollection"
                  component={SellerExpScreens.BadgesCollectionScreen}
                />
              </Stack.Group>

              {/* // Policies */}
              <Stack.Group>
                <Stack.Screen
                  name="PoliciesNewBusiness"
                  component={SellerExpScreens.PoliciesNewBusinessNavigator}
                />
              </Stack.Group>

              {/* // Pos */}
              <Stack.Group>
                <Stack.Screen
                  name="PoliciesPOS"
                  component={SellerExpScreens.POSNavigator}
                />
              </Stack.Group>

              {/* // Proposal Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="ProposalTable"
                  component={ProposalTableScreen}
                />
              </Stack.Group>
              <Stack.Screen name="EApp" component={EAppScreen} />
              <Stack.Screen name="ACR" component={ACRScreen} />
              <Stack.Screen
                name="SimulationTable"
                component={SimulationTableScreen}
                options={{
                  animation: 'fade',
                  orientation: isTabletMode ? 'landscape' : 'landscape_left',
                }}
              />

              <Stack.Screen
                name="FundIllustrationForm"
                component={FundIllustrationForm}
                options={{
                  animation: 'fade',
                  orientation: isTabletMode ? 'landscape' : 'landscape_left',
                }}
              />

              {/* // App Review Screens */}
              <Stack.Group screenOptions={{ headerShown: false }}>
                <Stack.Screen
                  name="PersonalInformationReview"
                  component={PersonalInformationReview}
                />
                <Stack.Screen
                  name="PolicyReplacementReview"
                  component={PolicyReplacementReview}
                />
                <Stack.Screen
                  name="DataPrivacyReview"
                  component={DataPrivacyReview}
                />
                <Stack.Screen
                  name="HealthQuestionsReview"
                  component={HealthQuestionsReview}
                />
                <Stack.Screen name="FatcaReview" component={FatcaReview} />
                <Stack.Screen name="RocReview" component={RocReview} />
                <Stack.Screen name="PdpReview" component={PdpReview} />
              </Stack.Group>

              {/* // Pdf Viewer Screen */}
              <Stack.Group>
                <Stack.Screen
                  name="PdfViewer"
                  component={PdfViewerScreen}
                  options={props => {
                    return {
                      animation: props.route.params.screenTransition
                        ? props.route.params.screenTransition
                        : 'slide_from_bottom',
                    };
                  }}
                />
                <Stack.Screen name="ImageList" component={ImageListScreen} />
              </Stack.Group>

              {/* // List Performance Screens */}
              <Stack.Group
                screenOptions={{
                  headerShown: true,
                  headerLeft: () => <CustomHeaderBackButton />,
                }}>
                <Stack.Screen
                  name="TeamListPerformance"
                  component={TeamPerformanceListScreen}
                  options={({ route }) => ({
                    headerTitle:
                      route.params?.viewType ===
                      TeamPerformanceViewType.Individual
                        ? 'View by individual'
                        : 'View by team',
                    headerTitleStyle: {
                      fontFamily: 'FWDCircularTT-Bold',
                      fontSize: typography.h7.size,
                    },
                  })}
                />
                <Stack.Screen
                  options={{ headerShown: false }}
                  name="TeamPerformanceDetails"
                  component={TeamPerformanceListScreen}
                />
              </Stack.Group>

              {/* // Team Operation Data Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="TeamOperation"
                  component={TeamOperationScreen}
                />
                <Stack.Screen name="AgentPolicies" component={PoliciesScreen} />
              </Stack.Group>

              {/* // Team Leads Conversion Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="TeamLeadsConversion"
                  component={SellerExpScreens.LeadsConversionScreen}
                />
              </Stack.Group>

              {/* // Team Target Screens */}
              <Stack.Group>
                <Stack.Screen name="TeamTarget" component={TeamTargetScreen} />
                <Stack.Screen
                  name="TeamMemberTargetsEdit"
                  component={TeamMemberTargetsEditScreen}
                />
              </Stack.Group>

              {/* // Group submission */}
              <Stack.Group>
                <Stack.Screen name="Submission" component={Submission} />
                <Stack.Screen
                  name="SubmissionFailed"
                  component={SubmissionFailed}
                />
              </Stack.Group>

              {/* // Team Performance Details Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="TeamView"
                  component={TeamPerformanceDetailsScreen}
                />
              </Stack.Group>

              {/* // Agent Performance Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="AgentPerformance"
                  component={AgentPerformanceDetailsScreen}
                />
              </Stack.Group>

              {/* // FNA */}
              <Stack.Group>
                <Stack.Screen
                  name="Fna"
                  component={FnaScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SavingsGoal"
                  component={SavingsGoal}
                  options={{
                    headerShown: false,
                    animation: 'slide_from_right',
                  }}
                />
                <Stack.Screen
                  name="ProtectionGoal"
                  component={ProtectionGoal}
                  options={{
                    headerShown: false,
                    animation: 'slide_from_right',
                  }}
                />
                <Stack.Screen
                  name="ProductRecommendation"
                  component={ProductRecommendationScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // CFF */}
              <Stack.Screen
                name="CustomerFactFind"
                component={CustomerFactFindScreen}
              />

              {/* // * ERecruit */}
              {countryModuleSellerConfig.ERecruit && (
                <>
                  <Stack.Screen
                    name="ERecruitApplication"
                    component={SellerExpScreens.ERecruitAppScreen}
                  />
                  <Stack.Screen
                    name="ERecruitApplicationStatus"
                    component={SellerExpScreens.ERecruitAppStatusScreen}
                  />
                  <Stack.Screen
                    name="ERecruitCandidateProfile"
                    component={SellerExpScreens.ERecruitCandidateProfileScreen}
                  />
                  <Stack.Screen
                    name="ERecruitCheckApplication"
                    component={SellerExpScreens.ERecruitCheckAppScreen}
                  />
                </>
              )}

              <Stack.Screen
                name="SocialMarketingCreateNew"
                component={SocialMarketingCreateNewScreen}
                options={{
                  presentation: 'modal',
                  headerShown: false,
                }}
              />

              {otherMenuTabsMobile
                .filter(menuItem => menuItem.navigationType === 'stack')
                .map(({ name, component }) => (
                  <Stack.Screen
                    key={'otherMenuTabsMobile_' + name}
                    name={name}
                    component={component}
                  />
                ))}
            </>
          ) : (
            // Unauthenticated Screens
            <Stack.Group>
              <Stack.Screen
                name="Login"
                component={LoginScreen}
                options={{
                  animationTypeForReplace: !isLoggedIn ? 'pop' : 'push',
                }}
              />
            </Stack.Group>
          )}
          {/* Common modal screens */}
        </Stack.Navigator>
      </View>
    </>
  );
}
